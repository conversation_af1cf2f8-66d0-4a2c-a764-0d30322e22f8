using UnityEngine;

namespace BoatControllerwithShooting
{
    public class BoatEngineSound : MonoBehaviour
    {
        public AudioSource engineSound;
        public float minPitch = 0.5f;
        public float maxPitch = 1.5f;
        public float pitchChangeSpeed = 0.1f;
        float moveInput;

        private void Update()
        {
            // Add null checks to prevent errors if singletons are not initialized yet
            if (BoatSystemManager.Instance == null) return;

            if (BoatSystemManager.Instance.controllerType == ControllerType.KeyboardMouse)
            {
                moveInput = Input.GetAxis("Vertical");
            }
            else
            {
                if (SimpleJoystick.Instance != null)
                    moveInput = SimpleJoystick.Instance.VerticalValue;
            }

            if (engineSound == null) return;
            float currentPitch = engineSound.pitch;

            if (Gasoline.Instance != null && Gasoline.Instance.CurrentFuel <= 0) moveInput = 0;

            if (moveInput > 0)
            {
                currentPitch = Mathf.MoveTowards(currentPitch, maxPitch, pitchChangeSpeed * Time.deltaTime);
            }
            else if (moveInput < 0)
            {
                currentPitch = Mathf.MoveTowards(currentPitch, minPitch, pitchChangeSpeed * Time.deltaTime);
            }
            else
            {
                currentPitch = Mathf.MoveTowards(currentPitch, 0f, pitchChangeSpeed * Time.deltaTime);
            }

            engineSound.pitch = currentPitch;
        }
    }
}