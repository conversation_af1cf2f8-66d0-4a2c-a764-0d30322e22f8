using UnityEngine;

public class OtherVehicleEnterexit : MonoBehaviour
{
    public GameObject helicopter, airplane, Motorbike, boat, train;
    public GameObject motorbikecam, helicoptercam, Playercam, airplanecam, traincam, boatcam;

    public GameObject helicoptercanvas, airplanecanvas, motorbikecanvas, boatcanvas, trainscanvas, Playercanvas;
    public Transform Player;

    public float distance = 10f;

    public GameObject enterbutton, exitbutton;

   
}
