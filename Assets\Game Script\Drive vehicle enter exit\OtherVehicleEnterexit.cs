using UnityEngine;
using System.Collections;

public class OtherVehicleEnterexit : MonoBehaviour
{
    [Header("Vehicles")]
    public GameObject helicopter, airplane, Motorbike, boat, train;

    [Header("Cameras")]
    public GameObject motorbikecam, helicoptercam, Playercam, airplanecam, traincam, boatcam;

    [Header("UI Canvas")]
    public GameObject helicoptercanvas, airplanecanvas, motorbikecanvas, boatcanvas, trainscanvas, Playercanvas;

    [<PERSON><PERSON>("Player")]
    public Transform Player;

    [Header("Settings")]
    public float distance = 10f;
    public float maxExitSpeed = 5f; // Maximum speed to allow exit (in km/h)

    [Header("UI Buttons")]
    public GameObject Helicopterenterbutton, Helicopterexitbutton;
    public GameObject Airplaneenterbutton, Airplaneexitbutton;
    public GameObject Motorbikeenterbutton, Motorbikeexitbutton;
    public GameObject Boatenterbutton, Boatexitbutton;
    public GameObject Trainenterbutton, Trainexitbutton;

    // Private variables to track state
    private bool isInVehicle = false;
    private GameObject currentVehicle = null;
    private Transform originalParent;

    void Start()
    {
        // Hide all buttons at start
        HideAllButtons();
    }

    void Update()
    {
        if (!isInVehicle)
        {
            CheckVehicleDistances();
        }
        else
        {
            CheckExitConditions();
        }
    }

    private void HideAllButtons()
    {
        Helicopterenterbutton.SetActive(false);
        Helicopterexitbutton.SetActive(false);
        Airplaneenterbutton.SetActive(false);
        Airplaneexitbutton.SetActive(false);
        Motorbikeenterbutton.SetActive(false);
        Motorbikeexitbutton.SetActive(false);
        Boatenterbutton.SetActive(false);
        Boatexitbutton.SetActive(false);
        Trainenterbutton.SetActive(false);
        Trainexitbutton.SetActive(false);
    }

    private void CheckVehicleDistances()
    {
        // Check helicopter distance
        if (helicopter != null && Vector3.Distance(Player.position, helicopter.transform.position) <= distance)
        {
            Helicopterenterbutton.SetActive(true);
        }
        else
        {
            Helicopterenterbutton.SetActive(false);
        }

        // Check airplane distance
        if (airplane != null && Vector3.Distance(Player.position, airplane.transform.position) <= distance)
        {
            Airplaneenterbutton.SetActive(true);
        }
        else
        {
            Airplaneenterbutton.SetActive(false);
        }

        // Check motorbike distance
        if (Motorbike != null && Vector3.Distance(Player.position, Motorbike.transform.position) <= distance)
        {
            Motorbikeenterbutton.SetActive(true);
        }
        else
        {
            Motorbikeenterbutton.SetActive(false);
        }

        // Check boat distance
        if (boat != null && Vector3.Distance(Player.position, boat.transform.position) <= distance)
        {
            Boatenterbutton.SetActive(true);
        }
        else
        {
            Boatenterbutton.SetActive(false);
        }

        // Check train distance
        if (train != null && Vector3.Distance(Player.position, train.transform.position) <= distance)
        {
            Trainenterbutton.SetActive(true);
        }
        else
        {
            Trainenterbutton.SetActive(false);
        }
    }

    private void CheckExitConditions()
    {
        if (currentVehicle == null) return;

        float vehicleSpeed = 0f;

        // Get speed from rigidbody velocity (universal method)
        var vehicleRigidbody = currentVehicle.GetComponent<Rigidbody>();
        if (vehicleRigidbody != null)
        {
            // Convert m/s to km/h
            vehicleSpeed = vehicleRigidbody.linearVelocity.magnitude * 3.6f;
        }

        // Show exit button based on vehicle type and speed
        if (currentVehicle == helicopter)
        {
            // Helicopter can exit anytime (no speed restriction)
            Helicopterexitbutton.SetActive(true);
        }
        else if (currentVehicle == airplane)
        {
            Airplaneexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle == Motorbike)
        {
            Motorbikeexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle == boat)
        {
            Boatexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
        else if (currentVehicle == train)
        {
            Trainexitbutton.SetActive(vehicleSpeed <= maxExitSpeed);
        }
    }

    // Helicopter Enter/Exit Functions
    public void EnterHelicopter()
    {
        EnterVehicle(helicopter, helicoptercam, helicoptercanvas);
    }

    public void ExitHelicopter()
    {
        ExitVehicle(helicopter, helicoptercam, helicoptercanvas);
    }

    // Airplane Enter/Exit Functions
    public void EnterAirplane()
    {
        EnterVehicle(airplane, airplanecam, airplanecanvas);
    }

    public void ExitAirplane()
    {
        ExitVehicle(airplane, airplanecam, airplanecanvas);
    }

    // Motorbike Enter/Exit Functions
    public void EnterMotorbike()
    {
        EnterVehicle(Motorbike, motorbikecam, motorbikecanvas);
    }

    public void ExitMotorbike()
    {
        ExitVehicle(Motorbike, motorbikecam, motorbikecanvas);
    }

    // Boat Enter/Exit Functions
    public void EnterBoat()
    {
        EnterVehicle(boat, boatcam, boatcanvas);
    }

    public void ExitBoat()
    {
        ExitVehicle(boat, boatcam, boatcanvas);
    }

    // Train Enter/Exit Functions
    public void EnterTrain()
    {
        EnterVehicle(train, traincam, trainscanvas);
    }

    public void ExitTrain()
    {
        ExitVehicle(train, traincam, trainscanvas);
    }

    // Generic Enter Vehicle Function
    private void EnterVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null) return;

        // Store original parent before parenting to vehicle
        originalParent = Player.parent;
        currentVehicle = vehicle;

        // Switch cameras and UI
        Playercam.SetActive(false);
        Player.gameObject.SetActive(false);
        vehicleCamera.SetActive(true);
        Playercanvas.SetActive(false);
        vehicleCanvas.SetActive(true);

        // Enable vehicle engine and controller
        EnableVehicleEngine(vehicle, true);

        // Enable any vehicle controller (generic approach)
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = true;
        }

        // Handle vehicle rigidbody
        var vehicleRigidbody = vehicle.GetComponent<Rigidbody>();
        if (vehicleRigidbody != null)
        {
            vehicleRigidbody.linearDamping = 0.01f;
            vehicleRigidbody.isKinematic = false;
        }

        // Parent player to vehicle and set position
        Player.SetParent(vehicle.transform);
        var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
        if (playerPosition != null && playerPosition.SeatPosition != null)
        {
            Player.position = playerPosition.SeatPosition.position;
        }

        // Hide all enter buttons and set vehicle state
        HideAllButtons();
        isInVehicle = true;
    }

    // Generic Exit Vehicle Function
    private void ExitVehicle(GameObject vehicle, GameObject vehicleCamera, GameObject vehicleCanvas)
    {
        if (vehicle == null || !isInVehicle) return;

        // Unparent player from vehicle first
        Player.SetParent(originalParent);

        // Switch cameras and UI
        Playercam.SetActive(true);
        Player.gameObject.SetActive(true);
        vehicleCamera.SetActive(false);
        Playercanvas.SetActive(true);
        vehicleCanvas.SetActive(false);

        // Set player position to door position (now unparented, so it's relative to world)
        var playerPosition = vehicle.GetComponent<VehiclePlayerPosition>();
        if (playerPosition != null && playerPosition.DoorPosition != null)
        {
            Player.position = playerPosition.DoorPosition.position;
        }

        // Disable vehicle engine
        EnableVehicleEngine(vehicle, false);

        // Handle vehicle rigidbody for stopping
        var vehicleRigidbody = vehicle.GetComponent<Rigidbody>();
        if (vehicleRigidbody != null)
        {
            vehicleRigidbody.linearDamping = 10f;
        }

        // Disable vehicle controller after delay
        StartCoroutine(DisableVehicleAfterDelay(vehicle));

        // Reset state
        currentVehicle = null;
        isInVehicle = false;
        HideAllButtons();
    }

    private IEnumerator DisableVehicleAfterDelay(GameObject vehicle)
    {
        yield return new WaitForSeconds(1f);

        // Disable any vehicle controller components
        var vehicleController = vehicle.GetComponent<MonoBehaviour>();
        if (vehicleController != null)
        {
            vehicleController.enabled = false;
        }
    }
}
