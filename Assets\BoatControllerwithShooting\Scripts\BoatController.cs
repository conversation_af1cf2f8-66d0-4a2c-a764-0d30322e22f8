using UnityEngine;

namespace BoatControllerwithShooting
{
    public class BoatController : MonoBehaviour
    {
        public float moveForce = 10f;
        public float rotateTorque = 10f;
        public Transform BoatBody;
        public static BoatController Instance;
        private Rigidbody rb;
        public GameObject ExplosionParticle;
        public Vector3 drag = new Vector3(6.0f, 4.0f, 0.2f);
        float rotationX = 0;
        public float Health = 100;
        private float TotalHealth = 100;
        float moveInput;
        float rotateInput;
        public float CurrentSpeed { get { return rb.linearVelocity.magnitude * 3.6f; } }
        public float MaxSpeed;
        public bool onWater = true;
        public GameObject splashParticle;
        public ParticleSystem waterTouchingParticle;
        public float rotationSpeed = 1f;

        private void Awake()
        {
            Instance = this;
            TotalHealth = Health;
        }

        private void Start()
        {
            rb = GetComponent<Rigidbody>();
            GameCanvas.Instance.Text_Health.text = "HEALTH: " + Health.ToString();
        }

        public void GetDamage(int Damage)
        {
            Health = Health - Damage;
            if (Health <= 0)
            {
                Health = 0;
                GameCanvas.Instance.Text_Health.text = "HEALTH: " + Health.ToString();
                ExplodeBoat();
                GameCanvas.Instance.GasolineUI.SetActive(false);
            }
            else
            {
                GameCanvas.Instance.Text_Health.text = "HEALTH: " + Health.ToString();
            }
        }

        private void HandleMotor()
        {
            // Allow motor operation if unlimited gasoline is enabled OR if there's fuel
            if ((Gasoline.Instance.UnlimitedGasoline || Gasoline.Instance.CurrentFuel > 0) && onWater)
            {
                Vector3 moveForceVector = transform.forward * moveInput * moveForce;
                if(CurrentSpeed < MaxSpeed)
                {
                    rb.AddForce(moveForceVector);
                }
                Vector3 dragDirection = transform.InverseTransformDirection(rb.linearVelocity);
                Vector3 dragForces = -Vector3.Scale(dragDirection, drag);

                rb.AddForce(transform.TransformDirection(dragForces));
                Vector3 rotateTorqueVector = Vector3.zero;
                if (Mathf.Abs(rb.linearVelocity.z) > 0.1f || Mathf.Abs(rb.linearVelocity.x) > 0.1f || Mathf.Abs(rb.linearVelocity.y) > 0.1f)
                {
                    rotateTorqueVector = transform.up * rotateInput * rotateTorque * Time.deltaTime;
                }

                if (moveInput == 0)
                {
                    rotationX = Mathf.Lerp(rotationX, moveInput, 0.05f);
                }
                rotationX += moveInput * Time.deltaTime * 5;
                rotationX = Mathf.Clamp(rotationX, -4, 4);
                BoatBody.localEulerAngles = new Vector3(rotationX * -1, BoatBody.localEulerAngles.y, BoatBody.localEulerAngles.z);
                transform.Rotate(rotateTorqueVector);

                // Only consume fuel if unlimited gasoline is disabled
                if (!Gasoline.Instance.UnlimitedGasoline)
                {
                    Gasoline.Instance.CurrentFuel = Gasoline.Instance.CurrentFuel - moveForceVector.magnitude * Time.deltaTime * Gasoline.Instance.FuelConsumptionRate;
                    if (Gasoline.Instance.CurrentFuel <= 0)
                    {
                        GetComponent<Rigidbody>().constraints = RigidbodyConstraints.FreezeRotation;
                    }
                }
            }
        }

        private void OnCollisionEnter(Collision collision)
        {
            if (collision.collider.CompareTag("Water"))
            {
                if (collision.relativeVelocity.magnitude > 10)
                {
                    Instantiate(splashParticle, collision.contacts[0].point, Quaternion.identity);
                }
            }
        }

        private void LateUpdate()
        {
            // Add null checks to prevent errors if singletons are not initialized yet
            if (GameCanvas.Instance != null && GameCanvas.Instance.Text_Speed != null)
            {
                GameCanvas.Instance.Text_Speed.text = Mathf.RoundToInt(CurrentSpeed).ToString();
            }

            if(onWater && Gasoline.Instance != null && (Gasoline.Instance.UnlimitedGasoline || Gasoline.Instance.CurrentFuel > 0))
            {
                Quaternion currentRotation = transform.rotation;
                Quaternion targetRotation = Quaternion.Euler(transform.rotation.eulerAngles.x, transform.rotation.eulerAngles.y, 0f);
                transform.rotation = Quaternion.Lerp(currentRotation, targetRotation, rotationSpeed * Time.deltaTime);
            }
        }

        void ExplodeBoat()
        {
            if (BoatSystemManager.Instance.cameraFPS.activeSelf)
            {
                BoatSystemManager.Instance.cameraFPS.SetActive(false);
                BoatSystemManager.Instance.cameraTPS.SetActive(true);
            }
            Instantiate(ExplosionParticle, transform.position, Quaternion.identity);
            Destroy(gameObject, 0.1f);
            GameCanvas.Instance.Hide_GameUI();
        }

        private void OnTriggerStay(Collider other)
        {
            if (other.CompareTag("Water"))
            {
                onWater = true;
                waterTouchingParticle.Play();
            }
        }

        private void OnTriggerExit(Collider other)
        {
            if (other.CompareTag("Water"))
            {
                onWater = false;
                if(waterTouchingParticle.isPlaying)
                {
                    waterTouchingParticle.Stop();
                }
            }
        }

        private void FixedUpdate()
        {
            // Add null check to prevent errors if BoatSystemManager is not initialized yet
            if (BoatSystemManager.Instance == null) return;

            if (BoatSystemManager.Instance.controllerType == ControllerType.KeyboardMouse)
            {
                moveInput = Input.GetAxis("Vertical");
                rotateInput = Input.GetAxis("Horizontal");
            }
            else
            {
                if (SimpleJoystick.Instance != null)
                {
                    moveInput = SimpleJoystick.Instance.VerticalValue;
                    rotateInput = SimpleJoystick.Instance.HorizontalValue;
                }
            }

            HandleMotor();
        }
    }
}