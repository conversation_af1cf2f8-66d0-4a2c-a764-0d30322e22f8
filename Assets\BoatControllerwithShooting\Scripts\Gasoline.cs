﻿using UnityEngine;

namespace BoatControllerwithShooting
{
    public class Gasoline : MonoBehaviour
    {
        public float FuelCapacity = 100;
        public float CurrentFuel = 100;
        public float FuelConsumptionRate = 0.05f;
        public bool UnlimitedGasoline = false;
        public AudioSource audioSource_Gasoline;
        public static Gasoline Instance;

        private void Awake()
        {
            Instance = this;
        }

        private void Start()
        {
            // Add null check to prevent errors if GameCanvas is not initialized yet
            if (GameCanvas.Instance != null && GameCanvas.Instance.Text_CurrentFuel != null)
            {
                GameCanvas.Instance.Text_CurrentFuel.text = CurrentFuel.ToString("F0");
            }
        }

        private void UpdateGasolineIndicators()
        {
            // Check if GameCanvas is available before updating UI
            if (GameCanvas.Instance == null) return;

            // Handle unlimited gasoline
            if (UnlimitedGasoline)
            {
                CurrentFuel = FuelCapacity; // Keep fuel at maximum
                if (GameCanvas.Instance.Slider_CurrentFuel != null)
                    GameCanvas.Instance.Slider_CurrentFuel.value = 100; // Show full tank
                if (GameCanvas.Instance.Text_CurrentFuel != null)
                    GameCanvas.Instance.Text_CurrentFuel.text = "∞"; // Show infinity symbol
                return;
            }

            if (CurrentFuel <= 0) CurrentFuel = 0;
            var energyAmountPercent = (CurrentFuel * 100) / FuelCapacity;

            if (GameCanvas.Instance.Slider_CurrentFuel != null)
                GameCanvas.Instance.Slider_CurrentFuel.value = energyAmountPercent;
            if (GameCanvas.Instance.Text_CurrentFuel != null)
                GameCanvas.Instance.Text_CurrentFuel.text = CurrentFuel.ToString("F0");
        }

        private void FixedUpdate()
        {
            UpdateGasolineIndicators();
        }

        public void Add_Gassoline(float Amount, AudioClip clip)
        {
            CurrentFuel += Amount;
            if (CurrentFuel > FuelCapacity)
            {
                CurrentFuel = FuelCapacity;
            }
            if (audioSource_Gasoline != null && audioSource_Gasoline.isPlaying)
                audioSource_Gasoline.Stop();
            if (audioSource_Gasoline != null)
                audioSource_Gasoline.PlayOneShot(clip);

            var rb = GetComponent<Rigidbody>();
            if (rb != null)
                rb.constraints = RigidbodyConstraints.None;

            // Update UI with null checks
            if (GameCanvas.Instance != null)
            {
                if (GameCanvas.Instance.Text_CurrentFuel != null)
                    GameCanvas.Instance.Text_CurrentFuel.text = CurrentFuel.ToString("F0");

                var energyAmountPercent = (CurrentFuel * 100) / FuelCapacity;
                if (GameCanvas.Instance.Slider_CurrentFuel != null)
                    GameCanvas.Instance.Slider_CurrentFuel.value = energyAmountPercent;
            }
        }
    }
}